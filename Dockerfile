# Use Python 3.11 slim image for smaller size
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies needed for ML packages
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the application code
COPY api/ ./api/
COPY ingestion/ ./ingestion/

# Set environment variables for Cloud Run
ENV PYTHONPATH=/app
ENV PORT=8080
ENV PYTHONUNBUFFERED=1

# Run the application directly with uvicorn
# Cloud Run will provide the PORT environment variable
# Increase timeout for ML model loading
CMD uvicorn api.main:app --host 0.0.0.0 --port $PORT --timeout-keep-alive 300
