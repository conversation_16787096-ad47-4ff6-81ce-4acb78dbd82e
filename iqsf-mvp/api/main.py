# ==============================================================================
#                  IQSF - MAIN API FILE (V5 - Wildcard CORS)
#        This final version uses a wildcard ("*") for CORS to ensure
#        maximum compatibility for local development and testing.
# ==============================================================================

from fastapi import FastAPI, Depends, HTTPException, Security
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security.api_key import APIKeyHeader
from pydantic import BaseModel, Field
from typing import List, Optional

# --- Centralized Imports from our application ---
# We import the "getter" functions for our services and the engine logic.
from .engine import LogosEngine
from .dependencies import (
    get_pinecone_index, 
    get_embedding_model, 
    get_classifier, 
    get_admin_api_key
)

# --- API Definition ---
# Initialize the FastAPI app with metadata for the documentation.
app = FastAPI(
    title="IQSF Global Guardian API",
    description="Real-time intelligence on LGBTQ+ safety and social risk, powered by the Logos AI Engine.",
    version="1.0.0"
)

# ==============================================================================
# --- CORS MIDDLEWARE CONFIGURATION ---
# This is the section that solves the "Failed to fetch" error for local dev.
# ==============================================================================
# Using a wildcard allows requests from any origin. This is perfect for
# local development but should be replaced with your specific frontend URL
# (e.g., "https://www.qsi.tech") in a production environment for security.
origins = ["*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods (GET, POST, PUT, etc.)
    allow_headers=["*"],  # Allow all headers (including X-API-KEY)
)
# ==============================================================================


# --- Security Dependency ---
# Defines how the API expects to receive the secret admin key.
API_KEY_NAME = "X-API-KEY"
api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=False)

async def get_api_key(
    api_key_from_request: str = Security(api_key_header),
    true_admin_key: str = Depends(get_admin_api_key)
):
    """
    Compares the key from the request header to the true key loaded securely
    from the environment, raising a 403 Forbidden error if they don't match.
    """
    if api_key_from_request == true_admin_key:
        return api_key_from_request
    else:
        raise HTTPException(
            status_code=403, 
            detail="Could not validate credentials. Invalid or missing API Key."
        )

# --- Engine Initialization ---
# This happens only once at startup, thanks to the singleton pattern.
logos_engine = LogosEngine(
    pinecone_index=get_pinecone_index(),
    embedding_model=get_embedding_model(),
    classifier=get_classifier()
)

# --- Pydantic Models for Data Validation & Response Shaping ---
class IngestRequest(BaseModel):
    urls: List[str] = Field(..., example=["https://www.hrw.org/topic/lgbt-rights"])

class Evidence(BaseModel):
    text: str
    source: str
    score: float

class QSIResponse(BaseModel):
    location: str
    assessment: str
    details: Optional[str] = None
    evidence_count: int = 0
    key_evidence: List[Evidence] = []

# --- API Endpoints ---

@app.get("/", tags=["Public"])
async def root():
    """A simple root endpoint to confirm the API is online."""
    return {"message": "Welcome to the IQSF Global Guardian API. See /docs for details."}


@app.get("/v1/qsi", response_model=QSIResponse, tags=["Public"])
async def get_qsi_score(location: str):
    """
    Get the Queer Safety Index (QSI) analysis for a specific location.
    e.g., ?location=Texas,USA or ?location=Amsterdam,Netherlands
    """
    if not location:
        raise HTTPException(status_code=400, detail="The 'location' query parameter is required.")
    
    # Delegate the core logic to our engine.
    result = logos_engine.calculate_qsi(location)
    return result


@app.post("/v1/admin/ingest", tags=["Admin"])
async def ingest_new_urls(request: IngestRequest, api_key: str = Depends(get_api_key)):
    """
    (Protected) Ingest new URLs into the knowledge base.
    Requires a valid X-API-KEY in the header.
    """
    # In a production system, this would trigger a background job.
    print(f"AUTHORIZED request received to ingest {len(request.urls)} URLs.")
    return {
        "status": "success", 
        "message": f"Request to ingest {len(request.urls)} URLs has been accepted and queued."
    }