# ==============================================================================
#                  IQSF - API DEPENDENCIES (V6 - Simplified for Development)
#        This version uses mock implementations when AI packages aren't available
#        to allow development with limited disk space.
# ==============================================================================

import os
from dotenv import load_dotenv

# Try to import AI packages, fall back to mocks if not available
try:
    import pinecone
    PINECONE_AVAILABLE = True
except ImportError:
    PINECONE_AVAILABLE = False
    print("⚠️  Pinecone not available - using mock implementation")

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    print("⚠️  SentenceTransformers not available - using mock implementation")

try:
    from transformers import pipeline
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    print("⚠️  Transformers not available - using mock implementation")

# --- Mock Classes for when packages aren't available ---
class MockPineconeIndex:
    def query(self, vector, top_k=5, include_metadata=True):
        # Return mock data for development
        return {
            'matches': [
                {
                    'score': 0.85,
                    'metadata': {
                        'text': 'Mock evidence about LGBTQ+ rights in this location.',
                        'source_url': 'https://example.com/mock-source'
                    }
                },
                {
                    'score': 0.72,
                    'metadata': {
                        'text': 'Additional mock evidence for testing purposes.',
                        'source_url': 'https://example.com/mock-source-2'
                    }
                }
            ]
        }

class MockEmbeddingModel:
    def encode(self, text):
        # Return a mock 768-dimensional vector
        import random
        return [random.random() for _ in range(768)]

class MockClassifier:
    def __call__(self, text, labels, multi_label=False):
        # Always return LGBTQ+ as the top label for testing
        return {
            'labels': ['LGBTQ rights and safety', 'climate change', 'economics', 'general philosophy'],
            'scores': [0.8, 0.1, 0.05, 0.05]
        }

# --- Load All Environment Variables from .env file ---
load_dotenv()
PINECONE_API_KEY = os.getenv("PINECONE_API_KEY")
ADMIN_API_KEY = os.getenv("ADMIN_API_KEY")

# --- Define Constants ---
# This is YOUR specific, unique host URL for the 768-dimension index.
PINECONE_INDEX_HOST = "https://iqsf-qsi-v1-1ckkiyl.svc.aped-4627-b74a.pinecone.io"

class EngineSingleton:
    """
    A singleton class to ensure our heavy AI models and database connections
    are initialized only ONCE when the API server starts up.
    """
    _instance = None

    def __init__(self):
        print("🚀 Initializing AI Services for the API server...")

        # Initialize Pinecone (real or mock)
        if PINECONE_AVAILABLE and PINECONE_API_KEY:
            try:
                pc = pinecone.Pinecone(api_key=PINECONE_API_KEY)
                self.pinecone_index = pc.Index(host=PINECONE_INDEX_HOST)
                print(f"🌲 Pinecone connection established to host: {PINECONE_INDEX_HOST}")
            except Exception as e:
                print(f"⚠️  Pinecone connection failed: {e}. Using mock implementation.")
                self.pinecone_index = MockPineconeIndex()
        else:
            print("🔧 Using mock Pinecone implementation for development")
            self.pinecone_index = MockPineconeIndex()

        # Initialize embedding model (real or mock)
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                print("   -> Loading embedding model (all-mpnet-base-v2)...")
                self.embedding_model = SentenceTransformer('all-mpnet-base-v2')
                print("   -> Embedding model loaded successfully")
            except Exception as e:
                print(f"⚠️  Failed to load embedding model: {e}. Using mock implementation.")
                self.embedding_model = MockEmbeddingModel()
        else:
            print("🔧 Using mock embedding model for development")
            self.embedding_model = MockEmbeddingModel()

        # Initialize classifier (real or mock)
        if TRANSFORMERS_AVAILABLE:
            try:
                device = 0 if torch.cuda.is_available() else -1
                print(f"🧠 Loading models on {'GPU' if device == 0 else 'CPU'}...")
                print("   -> Loading FAST zero-shot classifier (distilbert-base-uncased-mnli)...")
                self.classifier = pipeline(
                    "zero-shot-classification",
                    model="typeform/distilbert-base-uncased-mnli",
                    device=device
                )
                print("   -> Classifier loaded successfully")
            except Exception as e:
                print(f"⚠️  Failed to load classifier: {e}. Using mock implementation.")
                self.classifier = MockClassifier()
        else:
            print("🔧 Using mock classifier for development")
            self.classifier = MockClassifier()

        print("✅ All API models loaded and ready.")

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

# --- Global Singleton Instance ---
engine_services = EngineSingleton.get_instance()


# --- Dependency Injection Functions ---
def get_pinecone_index():
    return engine_services.pinecone_index

def get_embedding_model():
    return engine_services.embedding_model

def get_classifier():
    return engine_services.classifier

def get_admin_api_key():
    if not ADMIN_API_KEY:
        raise ValueError("ADMIN_API_KEY not found in environment. Please set it in the .env file.")
    return ADMIN_API_KEY