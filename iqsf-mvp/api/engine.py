import re
import pinecone
from sentence_transformers import SentenceTransformer
from transformers import pipeline
import torch

class LogosEngine:
    def __init__(self, pinecone_index, embedding_model, classifier):
        """
        Initializes the engine with pre-loaded models and DB connection.
        """
        self.index = pinecone_index
        self.embedding_model = embedding_model
        self.classifier = classifier
        self.domain_labels = ["LGBTQ rights and safety", "climate change", "economics", "general philosophy"]
        self.domain_of_expertise = "LGBTQ rights and safety"
        print("✅ LogosEngine initialized and ready.")

    def _is_in_domain(self, text: str) -> bool:
        """The Domain Guardrail."""
        print(f"--> [Guardrail] Checking domain for: '{text[:50]}...'")
        results = self.classifier(text, self.domain_labels, multi_label=False)
        top_label = results['labels'][0]
        return top_label == self.domain_of_expertise

    def _query_knowledge_base(self, claim: str, top_k: int = 5) -> list:
        """Queries Pinecone for relevant evidence."""
        print(f"--> [KB] Querying for: '{claim[:50]}...'")
        query_vector = self.embedding_model.encode(claim).tolist()
        
        results = self.index.query(
            vector=query_vector,
            top_k=top_k,
            include_metadata=True
        )
        
        evidence = []
        for match in results['matches']:
            # Filter for relevance
            if match['score'] > 0.3: # Relevance threshold
                evidence.append({
                    "text": match['metadata']['text'],
                    "source": match['metadata']['source_url'],
                    "score": match['score']
                })
        return evidence

    def calculate_qsi(self, location: str) -> dict:
        """
        The main public method for the API.
        Takes a location, queries the KB, and returns a structured response.
        """
        print(f"\n--- [LogosEngine] Calculating QSI for: {location} ---")
        
        # In a real system, the scoring model would be complex.
        # For the MVP, we just retrieve evidence. The "score" is implied by the data.
        if not self._is_in_domain(location):
             return {
                "location": location,
                "assessment": "OUT_OF_DOMAIN",
                "details": f"My expertise is in {self.domain_of_expertise}. I cannot provide an analysis for this location if it's unrelated."
            }
            
        evidence = self._query_knowledge_base(f"Safety and LGBTQ+ rights in {location}")

        if not evidence:
            return {
                "location": location,
                "assessment": "NO_DATA",
                "details": "Insufficient data in the knowledge base to provide a confident analysis for this specific location."
            }

        return {
            "location": location,
            "assessment": "DATA_AVAILABLE",
            "evidence_count": len(evidence),
            "key_evidence": evidence 
        }