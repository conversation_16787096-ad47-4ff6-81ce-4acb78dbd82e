import React, { useState } from 'react';
import axios from 'axios'; // For making API calls
import './App.css'; // We will create this file for styling
import logo from './logo.svg';

function App() {
  const [location, setLocation] = useState('');
  const [qsiResult, setQsiResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // IMPORTANT: This is the URL of your live backend API
  // For local testing, it's http://127.0.0.1:8000
  // When you deploy, it will be your AWS App Runner URL.
  const API_URL = 'http://127.0.0.1:8000/v1/qsi';

  const handleSearch = async (e) => {
    e.preventDefault(); // Prevents the form from reloading the page
    if (!location) {
      setError('Please enter a location.');
      return;
    }
    
    setIsLoading(true);
    setError('');
    setQsiResult(null);

    try {
      const response = await axios.get(API_URL, {
        params: { location: location }
      });
      setQsiResult(response.data);
    } catch (err) {
      console.error("API call failed:", err);
      setError('Failed to fetch data. The server might be down or the location not found.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>International Queer Safety Foundation</h1>
        <p className="subtitle">Real-time safety intelligence, powered by AI.</p>
      </header>
      
      <main>
        <div className="search-container">
          <p>Enter a city or country to get its latest Queer Safety Index (QSI) analysis.</p>
          <form onSubmit={handleSearch}>
            <input
              type="text"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              placeholder="e.g., Texas, USA or Amsterdam, Netherlands"
            />
            <button type="submit" disabled={isLoading}>
              {isLoading ? 'Analyzing...' : 'Get Safety Score'}
            </button>
          </form>
        </div>

        {error && <div className="error-message">{error}</div>}

        {qsiResult && (
          <div className="results-container">
            <h2>Analysis for: {qsiResult.location}</h2>
            <div className="assessment-box">
              <strong>Assessment:</strong> {qsiResult.assessment}
              {qsiResult.details && <p>{qsiResult.details}</p>}
            </div>

            {qsiResult.key_evidence && qsiResult.key_evidence.length > 0 && (
              <div className="evidence-section">
                <h3>Key Evidence ({qsiResult.evidence_count} points found):</h3>
                <ul>
                  {qsiResult.key_evidence.map((item, index) => (
                    <li key={index} className="evidence-card">
                      <p className="evidence-text">"{item.text}"</p>
                      <a href={item.source} target="_blank" rel="noopener noreferrer">
                        Source (Score: {item.score.toFixed(2)})
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </main>

      <footer className="App-footer">
        <p>© 2025 International Queer Safety Foundation. All rights reserved.</p>
      </footer>
    </div>
  );
}

export default App;
